#!/usr/bin/env python3
"""
Test script to verify .onion sites are accessible through Tor
This script will test the configured .onion addresses to ensure they're working
"""

import asyncio
import aiohttp
import aiohttp_socks
import yaml
import time
from pathlib import Path

async def test_onion_site(session, url, timeout=30):
    """Test if an .onion site is accessible"""
    try:
        print(f"Testing {url}...")
        start_time = time.time()

        async with session.get(url, timeout=aiohttp.ClientTimeout(total=timeout)) as response:
            elapsed = time.time() - start_time

            if response.status == 200:
                content = await response.text()
                print(f"✅ {url} - Status: {response.status} - Time: {elapsed:.2f}s - Size: {len(content)} bytes")
                return True, response.status, elapsed, len(content)
            else:
                print(f"⚠️  {url} - Status: {response.status} - Time: {elapsed:.2f}s")
                return False, response.status, elapsed, 0

    except asyncio.TimeoutError:
        elapsed = time.time() - start_time
        print(f"❌ {url} - Timeout after {elapsed:.2f}s")
        return False, "timeout", elapsed, 0

    except Exception as e:
        elapsed = time.time() - start_time
        print(f"❌ {url} - Error: {e} - Time: {elapsed:.2f}s")
        return False, str(e), elapsed, 0

async def create_tor_session():
    """Create a Tor session"""
    try:
        print("🔍 Creating Tor SOCKS proxy session...")

        # Create SOCKS connector
        connector = aiohttp_socks.ProxyConnector.from_url('socks5://127.0.0.1:9050')

        session = aiohttp.ClientSession(
            connector=connector,
            timeout=aiohttp.ClientTimeout(total=30),
            headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
        )

        # Test with a known working clearnet site through Tor
        print("Testing clearnet access through Tor...")
        async with session.get('http://httpbin.org/ip') as response:
            if response.status == 200:
                data = await response.json()
                print(f"✅ Tor connection working - Exit IP: {data.get('origin', 'unknown')}")
                return session
            else:
                print(f"❌ Tor connection failed - Status: {response.status}")
                await session.close()
                return None

    except Exception as e:
        print(f"❌ Tor connection test failed: {e}")
        if 'session' in locals():
            await session.close()
        return None

async def main():
    """Main test function"""
    print("🧅 Onion Site Accessibility Test")
    print("=" * 50)

    # Load configuration
    config_path = Path("config.yaml")
    if not config_path.exists():
        print("❌ config.yaml not found!")
        return

    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)

    # Get .onion sites from config
    darkweb_config = config.get('leak_detection', {}).get('sources', {}).get('darkweb', {})
    onion_sites = darkweb_config.get('sites', [])

    if not onion_sites:
        print("❌ No .onion sites configured!")
        return

    print(f"Found {len(onion_sites)} .onion sites to test")
    print()

    # Test Tor connection first
    session = await create_tor_session()
    if not session:
        print("\n❌ Cannot proceed - Tor connection failed")
        print("Make sure Tor is running on 127.0.0.1:9050")
        return

    print("\n🧅 Testing .onion sites...")
    print("-" * 30)

    # Test each .onion site
    results = []
    for site in onion_sites:
        # Skip comment lines
        if site.strip().startswith('#') or not site.strip():
            continue

        # Clean up the URL (remove comments)
        url = site.split('#')[0].strip()
        if not url:
            continue

        success, status, elapsed, size = await test_onion_site(session, url)
        results.append({
            'url': url,
            'success': success,
            'status': status,
            'elapsed': elapsed,
            'size': size
        })

        # Small delay between requests
        await asyncio.sleep(2)

    await session.close()

    # Summary
    print("\n📊 Test Summary")
    print("=" * 30)

    successful = [r for r in results if r['success']]
    failed = [r for r in results if not r['success']]

    print(f"✅ Successful: {len(successful)}/{len(results)}")
    print(f"❌ Failed: {len(failed)}/{len(results)}")

    if successful:
        print("\n✅ Working sites:")
        for result in successful:
            print(f"  • {result['url']} ({result['elapsed']:.2f}s)")

    if failed:
        print("\n❌ Failed sites:")
        for result in failed:
            print(f"  • {result['url']} - {result['status']}")

    print("\n💡 Notes:")
    print("• .onion addresses change frequently")
    print("• Some sites may be temporarily down")
    print("• Consider updating addresses if many fail")
    print("• Always verify sites are legitimate before adding")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⚠️  Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
