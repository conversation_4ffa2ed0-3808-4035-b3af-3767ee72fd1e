#!/usr/bin/env python3
"""
Script to enable darkweb scanning in PassChanger
This script will:
1. Check if Tor is installed
2. Enable darkweb scanning in config.yaml
3. Provide instructions for manual setup if needed
"""

import os
import shutil
import subprocess
import sys
import yaml
from pathlib import Path

def check_tor_installation():
    """Check if Tor is installed on the system"""
    print("🔍 Checking Tor installation...")
    
    if shutil.which('tor'):
        print("✅ Tor is installed")
        
        # Try to get Tor version
        try:
            result = subprocess.run(['tor', '--version'], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version_line = result.stdout.split('\n')[0]
                print(f"   Version: {version_line}")
            return True
        except Exception as e:
            print(f"   Warning: Could not get Tor version: {e}")
            return True
    else:
        print("❌ Tor is not installed")
        return False

def install_tor_instructions():
    """Provide instructions for installing Tor"""
    print("\n📋 To install Tor:")
    print("\nUbuntu/Debian:")
    print("  sudo apt update && sudo apt install tor")
    print("\nFedora/RHEL:")
    print("  sudo dnf install tor")
    print("\nArch Linux:")
    print("  sudo pacman -S tor")
    print("\nMacOS (with Homebrew):")
    print("  brew install tor")
    print("\nWindows:")
    print("  Download from: https://www.torproject.org/download/")
    print("\nAfter installation, run this script again.")

def enable_darkweb_config():
    """Enable darkweb scanning in config.yaml"""
    config_path = Path("config.yaml")
    
    if not config_path.exists():
        print("❌ config.yaml not found!")
        return False
    
    print("📝 Updating config.yaml...")
    
    try:
        # Load current config
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Enable darkweb scanning
        if 'leak_detection' in config and 'sources' in config['leak_detection']:
            if 'darkweb' in config['leak_detection']['sources']:
                config['leak_detection']['sources']['darkweb']['enabled'] = True
                print("✅ Darkweb scanning enabled in configuration")
            else:
                print("❌ Darkweb configuration not found in config.yaml")
                return False
        else:
            print("❌ Leak detection configuration not found in config.yaml")
            return False
        
        # Save updated config
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)
        
        print("✅ Configuration updated successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return False

def check_tor_service():
    """Check if Tor service is running"""
    print("\n🔍 Checking Tor service status...")
    
    try:
        # Try to connect to default Tor SOCKS port
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('127.0.0.1', 9050))
        sock.close()
        
        if result == 0:
            print("✅ Tor SOCKS proxy is running on port 9050")
            return True
        else:
            print("❌ Tor SOCKS proxy is not running on port 9050")
            return False
            
    except Exception as e:
        print(f"❌ Error checking Tor service: {e}")
        return False

def start_tor_instructions():
    """Provide instructions for starting Tor"""
    print("\n📋 To start Tor:")
    print("\nSystemd (Ubuntu/Debian/Fedora/Arch):")
    print("  sudo systemctl start tor")
    print("  sudo systemctl enable tor  # To start automatically on boot")
    print("\nManual start:")
    print("  tor")
    print("\nNote: PassChanger can also auto-start Tor if configured to do so.")

def main():
    """Main function"""
    print("🔐 PassChanger Darkweb Scanner Setup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not Path("config.yaml").exists():
        print("❌ Please run this script from the PassChanger directory")
        sys.exit(1)
    
    # Check Tor installation
    tor_installed = check_tor_installation()
    
    if not tor_installed:
        install_tor_instructions()
        print("\n❌ Please install Tor first, then run this script again.")
        sys.exit(1)
    
    # Enable darkweb config
    config_updated = enable_darkweb_config()
    
    if not config_updated:
        print("\n❌ Failed to update configuration")
        sys.exit(1)
    
    # Check if Tor is running
    tor_running = check_tor_service()
    
    if not tor_running:
        start_tor_instructions()
        print("\n⚠️  Tor is not currently running.")
        print("   You can either start it manually or let PassChanger auto-start it.")
    
    print("\n🎉 Setup complete!")
    print("\nNext steps:")
    print("1. Run PassChanger: python main.py")
    print("2. Go to 'Settings' to verify Tor status")
    print("3. Use 'Tor management' menu to control Tor")
    print("4. Run a leak detection scan to test darkweb functionality")
    
    print("\n⚠️  Important Security Notes:")
    print("• Darkweb scanning uses Tor for anonymity")
    print("• Be cautious about what sites you configure")
    print("• Only use legitimate .onion sites for leak detection")
    print("• Consider using a VPN in addition to Tor for extra privacy")

if __name__ == "__main__":
    main()
