# Tor Integration for Darkweb Leak Detection

PassChanger includes Tor integration to enable darkweb scanning for leaked credentials. This feature allows the application to search .onion sites and darkweb forums for potential account breaches.

## Features

- **Automatic Tor Management**: Start, stop, and restart Tor from within PassChanger
- **Circuit Management**: Request new Tor circuits for enhanced anonymity
- **Proxy Integration**: Seamless SOCKS5 proxy integration with aiohttp
- **Security-First**: Built with privacy and security best practices
- **Status Monitoring**: Real-time Tor status and connection monitoring

## Prerequisites

### 1. Install Tor

**Ubuntu/Debian:**
```bash
sudo apt update && sudo apt install tor
```

**Fedora/RHEL:**
```bash
sudo dnf install tor
```

**Arch Linux:**
```bash
sudo pacman -S tor
```

**macOS (with Homebrew):**
```bash
brew install tor
```

**Windows:**
Download from: https://www.torproject.org/download/

### 2. Install Python Dependencies

The required dependencies are already included in the project:
- `aiohttp-socks>=0.8.4` - SOCKS proxy support for aiohttp
- `stem>=1.8.2` - Tor control library
- `pysocks>=1.7.1` - SOCKS proxy support

## Quick Setup

Run the automated setup script:

```bash
python enable_darkweb.py
```

This script will:
1. Check if Tor is installed
2. Enable darkweb scanning in config.yaml
3. Provide setup instructions if needed

## Manual Configuration

### 1. Enable Darkweb Scanning

Edit `config.yaml`:

```yaml
leak_detection:
  sources:
    darkweb:
      enabled: true  # Change from false to true
      tor_proxy: "socks5://127.0.0.1:9050"
      tor_control_port: 9051
      auto_start_tor: true
      sites:
        - "http://3g2upl4pq6kufc4m.onion"  # Example .onion site
        # Add more legitimate .onion sites here
```

### 2. Configure Tor Sites

Add legitimate .onion sites to the `sites` list in config.yaml. These should be:
- Paste sites that might contain leaked data
- Security research sites
- Breach notification services
- **NOT** illegal marketplaces or services

### 3. Start Tor Service

**Using systemd:**
```bash
sudo systemctl start tor
sudo systemctl enable tor  # Auto-start on boot
```

**Manual start:**
```bash
tor
```

**Let PassChanger manage it:**
Set `auto_start_tor: true` in config.yaml and PassChanger will start Tor automatically.

## Usage

### 1. Start PassChanger

```bash
python main.py
```

### 2. Tor Management Menu

From the main menu, select "6. Tor management" to:
- Start/stop Tor
- Restart Tor
- Request new circuits
- View detailed status

### 3. Run Darkweb Scan

Select "1. Run leak detection scan" from the main menu. If darkweb scanning is enabled and Tor is running, it will include darkweb sources in the scan.

## Configuration Options

### Tor Settings

```yaml
darkweb:
  enabled: true                           # Enable/disable darkweb scanning
  tor_proxy: "socks5://127.0.0.1:9050"   # Tor SOCKS proxy URL
  tor_control_port: 9051                  # Tor control port
  tor_control_password: ""                # Control password (optional)
  auto_start_tor: true                    # Auto-start Tor if not running
  tor_data_dir: "data/tor"               # Tor data directory
  circuit_timeout: 30                     # Circuit timeout in seconds
  max_retries: 3                         # Max retries for failed requests
```

### Search Configuration

```yaml
darkweb:
  sites:
    - "http://3g2upl4pq6kufc4m.onion"     # DuckDuckGo onion
    # Add more .onion sites here
  search_terms:
    - "database dump"
    - "leaked passwords"
    - "credentials"
    - "email list"
```

## Security Considerations

### Privacy
- All darkweb requests go through Tor for anonymity
- New circuits are requested between searches
- No logs are kept of .onion sites visited
- User agents are randomized

### Safety
- Only configure legitimate .onion sites
- Avoid illegal marketplaces or services
- Consider using a VPN in addition to Tor
- Be aware of your local laws regarding Tor usage

### Performance
- Tor connections are slower than direct connections
- Darkweb scans take longer due to circuit changes
- Rate limiting is enforced to avoid detection

## Troubleshooting

### Tor Won't Start
1. Check if Tor is installed: `which tor`
2. Check if port 9050 is available: `netstat -ln | grep 9050`
3. Check Tor logs in the application or system logs
4. Try starting Tor manually: `tor`

### Connection Issues
1. Verify Tor is running: Check "Tor management" menu
2. Test SOCKS proxy: `curl --socks5 127.0.0.1:9050 http://httpbin.org/ip`
3. Request new circuit: Use "New circuit" option in Tor menu
4. Restart Tor: Use "Restart Tor" option

### No Results from Darkweb Scan
1. Verify .onion sites are accessible
2. Check if sites require special authentication
3. Review search terms in configuration
4. Check application logs for errors

## Legal and Ethical Considerations

- **Legal Compliance**: Ensure Tor usage is legal in your jurisdiction
- **Ethical Use**: Only use for legitimate security research and breach detection
- **Responsible Disclosure**: Report found breaches through proper channels
- **No Illegal Activity**: Do not use for accessing illegal content or services

## Advanced Configuration

### Custom Tor Configuration

You can customize Tor by editing the generated `data/tor/torrc` file or by modifying the Tor manager configuration in the code.

### Multiple Tor Instances

For advanced users, you can run multiple Tor instances on different ports and configure PassChanger to use specific instances.

### Integration with External Tor

If you have an existing Tor setup, disable `auto_start_tor` and configure the proxy settings to point to your existing Tor instance.

## Support

If you encounter issues with Tor integration:

1. Check the application logs in `logs/passchanger.log`
2. Verify your Tor installation and configuration
3. Test Tor connectivity independently
4. Review the security considerations and ensure proper setup

For additional help, refer to the main PassChanger documentation or the Tor Project documentation at https://www.torproject.org/docs/
