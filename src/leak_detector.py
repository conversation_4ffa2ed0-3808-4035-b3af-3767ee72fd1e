"""
Leak Detection Module for PassChanger
Scans various sources for potential account leaks and breaches
"""

import asyncio
import logging
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import aiohttp
import requests
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
import validators
import aiohttp_socks

logger = logging.getLogger("PassChanger.LeakDetector")

class LeakDetector:
    def __init__(self, config: dict, ai_engine, db_manager, tor_manager=None):
        self.config = config
        self.leak_config = config['leak_detection']
        self.ai_engine = ai_engine
        self.db_manager = db_manager
        self.tor_manager = tor_manager
        self.user_agent = UserAgent()
        self.session = None
        self.tor_session = None

    async def initialize(self):
        """Initialize leak detector"""
        try:
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': self.user_agent.random}
            )

            # Create Tor session if Tor manager is available
            if self.tor_manager and self.leak_config['sources']['darkweb']['enabled']:
                await self._create_tor_session()

            logger.info("Leak detector initialized")

        except Exception as e:
            logger.error(f"Failed to initialize leak detector: {e}")
            raise

    async def scan_for_leaks(self) -> List[Dict[str, Any]]:
        """Main method to scan for leaks across all sources"""
        all_results = []

        try:
            # Get all accounts to monitor
            accounts = await self.db_manager.get_all_accounts()

            if not accounts:
                logger.info("No accounts to monitor")
                return []

            logger.info(f"Scanning {len(accounts)} accounts for leaks")

            # Scan each enabled source
            if self.leak_config['sources']['haveibeenpwned']['enabled']:
                hibp_results = await self._scan_haveibeenpwned(accounts)
                all_results.extend(hibp_results)

            if self.leak_config['sources']['custom_searches']['enabled']:
                search_results = await self._scan_search_engines(accounts)
                all_results.extend(search_results)

            if self.leak_config['sources']['darkweb']['enabled']:
                darkweb_results = await self._scan_darkweb(accounts)
                all_results.extend(darkweb_results)

            # Process results with AI analysis
            processed_results = await self._process_results_with_ai(all_results, accounts)

            # Store results in database
            for result in processed_results:
                await self.db_manager.record_leak_detection(result)

            logger.info(f"Leak scan completed. Found {len(processed_results)} potential issues")
            return processed_results

        except Exception as e:
            logger.error(f"Error during leak scan: {e}")
            return []

    async def _scan_haveibeenpwned(self, accounts: List[Dict]) -> List[Dict[str, Any]]:
        """Scan HaveIBeenPwned for account breaches"""
        results = []
        hibp_config = self.leak_config['sources']['haveibeenpwned']

        for account in accounts:
            try:
                email = account['email']

                # Check breaches
                breach_url = f"https://haveibeenpwned.com/api/v3/breachedaccount/{email}"
                headers = {
                    'User-Agent': hibp_config['user_agent'],
                    'hibp-api-key': hibp_config.get('api_key', '')
                }

                async with self.session.get(breach_url, headers=headers) as response:
                    if response.status == 200:
                        breaches = await response.json()

                        for breach in breaches:
                            results.append({
                                'account_id': account['id'],
                                'source': 'haveibeenpwned',
                                'detection_type': 'breach',
                                'risk_level': 'high',
                                'confidence': 0.9,
                                'description': f"Account found in {breach['Name']} breach",
                                'raw_data': json.dumps(breach),
                                'metadata': {
                                    'breach_name': breach['Name'],
                                    'breach_date': breach['BreachDate'],
                                    'compromised_data': breach['DataClasses']
                                }
                            })

                    elif response.status == 404:
                        # No breaches found - this is good
                        logger.debug(f"No breaches found for {email}")

                    elif response.status == 429:
                        # Rate limited
                        logger.warning("Rate limited by HaveIBeenPwned")
                        await asyncio.sleep(2)

                    else:
                        logger.warning(f"HIBP API error {response.status} for {email}")

                # Check pastes
                paste_url = f"https://haveibeenpwned.com/api/v3/pasteaccount/{email}"

                async with self.session.get(paste_url, headers=headers) as response:
                    if response.status == 200:
                        pastes = await response.json()

                        for paste in pastes:
                            results.append({
                                'account_id': account['id'],
                                'source': 'haveibeenpwned',
                                'detection_type': 'paste',
                                'risk_level': 'medium',
                                'confidence': 0.8,
                                'description': f"Email found in paste: {paste['Title']}",
                                'raw_data': json.dumps(paste),
                                'metadata': {
                                    'paste_id': paste['Id'],
                                    'paste_date': paste['Date'],
                                    'paste_source': paste['Source']
                                }
                            })

                # Rate limiting
                await asyncio.sleep(1.5)  # HIBP requires 1.5s between requests

            except Exception as e:
                logger.error(f"Error checking HIBP for {account['email']}: {e}")

        return results

    async def _scan_search_engines(self, accounts: List[Dict]) -> List[Dict[str, Any]]:
        """Scan search engines for leaked account information"""
        results = []
        search_config = self.leak_config['sources']['custom_searches']

        for account in accounts:
            try:
                email = account['email']
                username = account.get('username', '')

                # Create search queries
                queries = [
                    f'"{email}" leak OR breach OR dump OR database',
                    f'"{email}" password OR credentials OR login',
                ]

                if username:
                    queries.extend([
                        f'"{username}" "{email}" leak OR breach',
                        f'"{username}" password OR credentials'
                    ])

                for query in queries:
                    # Search Google (using custom search or scraping)
                    google_results = await self._search_google(query)

                    for result in google_results:
                        # Analyze result with AI
                        analysis = await self.ai_engine.analyze_leak_data(
                            result['snippet'],
                            {
                                'emails': [email],
                                'usernames': [username] if username else [],
                                'search_query': query
                            }
                        )

                        if analysis['risk_level'] not in ['none', 'unknown']:
                            results.append({
                                'account_id': account['id'],
                                'source': 'search_engine',
                                'detection_type': 'search_result',
                                'risk_level': analysis['risk_level'],
                                'confidence': analysis['confidence'],
                                'description': f"Potential leak found in search: {result['title']}",
                                'raw_data': json.dumps(result),
                                'ai_analysis': analysis,
                                'metadata': {
                                    'url': result['url'],
                                    'search_query': query
                                }
                            })

                # Rate limiting
                await asyncio.sleep(2)

            except Exception as e:
                logger.error(f"Error in search engine scan for {account['email']}: {e}")

        return results

    async def _search_google(self, query: str) -> List[Dict[str, Any]]:
        """Search Google for the given query"""
        results = []

        try:
            # Use DuckDuckGo as it's more privacy-friendly and doesn't require API keys
            search_url = "https://duckduckgo.com/html/"
            params = {
                'q': query,
                'kl': 'us-en'
            }

            headers = {
                'User-Agent': self.user_agent.random
            }

            async with self.session.get(search_url, params=params, headers=headers) as response:
                if response.status == 200:
                    html = await response.text()
                    soup = BeautifulSoup(html, 'html.parser')

                    # Parse search results
                    for result_div in soup.find_all('div', class_='result')[:5]:  # Limit to 5 results
                        title_elem = result_div.find('a', class_='result__a')
                        snippet_elem = result_div.find('a', class_='result__snippet')

                        if title_elem and snippet_elem:
                            results.append({
                                'title': title_elem.get_text(strip=True),
                                'url': title_elem.get('href', ''),
                                'snippet': snippet_elem.get_text(strip=True)
                            })

        except Exception as e:
            logger.error(f"Error searching for '{query}': {e}")

        return results

    async def _scan_darkweb(self, accounts: List[Dict]) -> List[Dict[str, Any]]:
        """Scan dark web sources (requires Tor setup)"""
        results = []

        if not self.tor_manager:
            logger.warning("Tor manager not available for darkweb scanning")
            return results

        if not await self.tor_manager.is_tor_running():
            logger.warning("Tor is not running, skipping darkweb scan")
            return results

        if not self.tor_session:
            await self._create_tor_session()

        if not self.tor_session:
            logger.error("Failed to create Tor session")
            return results

        darkweb_config = self.leak_config['sources']['darkweb']
        sites = darkweb_config.get('sites', [])
        search_terms = darkweb_config.get('search_terms', [])
        max_retries = darkweb_config.get('max_retries', 3)

        logger.info(f"Starting darkweb scan for {len(accounts)} accounts")

        for account in accounts:
            try:
                email = account['email']
                username = account.get('username', '')

                # Create search queries specific to this account
                account_search_terms = []
                account_search_terms.extend([f'"{email}"', email.split('@')[0]])
                if username:
                    account_search_terms.append(f'"{username}"')

                # Combine with general search terms
                combined_terms = []
                for account_term in account_search_terms:
                    for general_term in search_terms:
                        combined_terms.append(f"{account_term} {general_term}")

                # Search each configured site
                for site_url in sites:
                    for search_term in combined_terms[:5]:  # Limit to 5 searches per site
                        try:
                            # Request new circuit for each search for better anonymity
                            circuit_changed = await self.tor_manager.new_circuit()
                            if circuit_changed:
                                await asyncio.sleep(2)  # Wait for circuit change
                            else:
                                logger.debug("Circuit renewal not available, continuing with existing connection")

                            search_results = await self._search_darkweb_site(
                                site_url, search_term, max_retries
                            )

                            for result in search_results:
                                # Analyze result with AI
                                analysis = await self.ai_engine.analyze_leak_data(
                                    result['content'],
                                    {
                                        'emails': [email],
                                        'usernames': [username] if username else [],
                                        'search_query': search_term,
                                        'source': 'darkweb'
                                    }
                                )

                                if analysis['risk_level'] not in ['none', 'unknown']:
                                    results.append({
                                        'account_id': account['id'],
                                        'source': 'darkweb',
                                        'detection_type': 'darkweb_search',
                                        'risk_level': analysis['risk_level'],
                                        'confidence': analysis['confidence'],
                                        'description': f"Potential leak found on darkweb: {result['title']}",
                                        'raw_data': json.dumps(result),
                                        'ai_analysis': analysis,
                                        'metadata': {
                                            'site_url': site_url,
                                            'search_term': search_term,
                                            'tor_circuit': True
                                        }
                                    })

                            # Rate limiting between searches
                            await asyncio.sleep(5)

                        except Exception as e:
                            logger.error(f"Error searching darkweb site {site_url} for {search_term}: {e}")
                            continue

                # Rate limiting between accounts
                await asyncio.sleep(10)

            except Exception as e:
                logger.error(f"Error in darkweb scan for {account['email']}: {e}")

        logger.info(f"Darkweb scan completed. Found {len(results)} potential issues")
        return results

    async def _process_results_with_ai(self, raw_results: List[Dict],
                                     accounts: List[Dict]) -> List[Dict[str, Any]]:
        """Process raw results with AI analysis"""
        processed_results = []

        for result in raw_results:
            try:
                # If not already analyzed by AI, do it now
                if 'ai_analysis' not in result:
                    account = next((a for a in accounts if a['id'] == result['account_id']), None)

                    if account:
                        context = {
                            'emails': [account['email']],
                            'usernames': [account.get('username')] if account.get('username') else [],
                            'account_name': account['name']
                        }

                        analysis = await self.ai_engine.analyze_leak_data(
                            result.get('raw_data', ''),
                            context
                        )

                        result['ai_analysis'] = analysis

                        # Update risk level based on AI analysis
                        if analysis['risk_level'] != 'unknown':
                            result['risk_level'] = analysis['risk_level']
                            result['confidence'] = analysis['confidence']

                # Only include results with meaningful risk levels
                if result['risk_level'] not in ['none', 'unknown']:
                    processed_results.append(result)

                    # Update account status in database
                    await self.db_manager.update_account_status(
                        result['account_id'],
                        'at_risk' if result['risk_level'] in ['high', 'critical'] else 'warning',
                        result['risk_level']
                    )

            except Exception as e:
                logger.error(f"Error processing result: {e}")

        return processed_results

    async def _create_tor_session(self):
        """Create HTTP session with Tor proxy"""
        try:
            if not self.tor_manager:
                return

            darkweb_config = self.leak_config['sources']['darkweb']
            proxy_url = darkweb_config.get('tor_proxy', 'socks5://127.0.0.1:9050')

            # Create SOCKS connector
            connector = aiohttp_socks.ProxyConnector.from_url(proxy_url)

            # Create session with Tor proxy
            self.tor_session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=darkweb_config.get('circuit_timeout', 30)),
                headers={'User-Agent': self.user_agent.random}
            )

            logger.info("Tor session created successfully")

        except Exception as e:
            logger.error(f"Failed to create Tor session: {e}")
            self.tor_session = None

    async def _search_darkweb_site(self, site_url: str, search_term: str, max_retries: int = 3) -> List[Dict[str, Any]]:
        """Search a specific darkweb site for the given term"""
        results = []

        for attempt in range(max_retries):
            try:
                # For this example, we'll do a simple GET request to the site
                # In a real implementation, you'd need to understand each site's search API/format

                # Simple approach: try to access the site and look for the search term in content
                async with self.tor_session.get(site_url) as response:
                    if response.status == 200:
                        content = await response.text()

                        # Basic search for the term in the content
                        if search_term.lower() in content.lower():
                            # Parse the content for relevant information
                            soup = BeautifulSoup(content, 'html.parser')

                            # Extract title
                            title = soup.title.string if soup.title else "Unknown"

                            # Look for relevant sections containing the search term
                            relevant_sections = []
                            for element in soup.find_all(['p', 'div', 'span'], string=lambda text: text and search_term.lower() in text.lower()):
                                relevant_sections.append(element.get_text(strip=True))

                            if relevant_sections:
                                results.append({
                                    'title': title,
                                    'url': site_url,
                                    'content': ' '.join(relevant_sections[:3]),  # Limit content
                                    'search_term': search_term,
                                    'found_sections': len(relevant_sections)
                                })

                        break  # Success, no need to retry

                    elif response.status == 403:
                        logger.warning(f"Access denied to {site_url}")
                        break  # Don't retry on access denied

                    else:
                        logger.warning(f"HTTP {response.status} from {site_url}")

            except asyncio.TimeoutError:
                logger.warning(f"Timeout accessing {site_url} (attempt {attempt + 1})")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)  # Wait before retry

            except Exception as e:
                logger.error(f"Error accessing {site_url}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(5)  # Wait before retry

        return results

    async def close(self):
        """Close HTTP sessions"""
        if self.session:
            await self.session.close()
            # Give a small delay to ensure proper cleanup
            await asyncio.sleep(0.1)
            logger.info("Leak detector HTTP session closed")

        if self.tor_session:
            await self.tor_session.close()
            await asyncio.sleep(0.1)
            logger.info("Leak detector Tor session closed")
