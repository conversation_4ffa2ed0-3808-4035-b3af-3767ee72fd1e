"""
Tor Manager Module for PassChanger
Handles Tor process lifecycle and proxy management for darkweb scanning
"""

import asyncio
import logging
import os
import shutil
import socket
import subprocess
import tempfile
import time
from pathlib import Path
from typing import Optional, Dict, Any

import stem
from stem import Signal
from stem.control import Controller
from stem.process import launch_tor_with_config

logger = logging.getLogger("PassChanger.TorManager")


class TorManager:
    def __init__(self, config: dict):
        self.config = config
        self.darkweb_config = config['leak_detection']['sources']['darkweb']
        self.tor_process = None
        self.controller = None
        self.is_running = False
        self.tor_data_dir = None
        self.controller_connection_attempted = False
        self.controller_available = False

        # Extract configuration
        self.tor_proxy_host = "127.0.0.1"
        self.tor_proxy_port = 9050
        self.tor_control_port = self.darkweb_config.get('tor_control_port', 9051)
        self.tor_control_password = self.darkweb_config.get('tor_control_password', '')
        self.auto_start = self.darkweb_config.get('auto_start_tor', True)
        self.circuit_timeout = self.darkweb_config.get('circuit_timeout', 30)

        # Parse proxy URL
        proxy_url = self.darkweb_config.get('tor_proxy', 'socks5://127.0.0.1:9050')
        if '://' in proxy_url:
            parts = proxy_url.split('://', 1)[1].split(':')
            if len(parts) == 2:
                self.tor_proxy_host = parts[0]
                self.tor_proxy_port = int(parts[1])

    async def initialize(self):
        """Initialize Tor manager"""
        try:
            # Create Tor data directory
            data_dir = self.darkweb_config.get('tor_data_dir', 'data/tor')
            self.tor_data_dir = Path(data_dir)
            self.tor_data_dir.mkdir(parents=True, exist_ok=True)

            logger.info("Tor manager initialized")

            # Auto-start Tor if configured
            if self.auto_start and not await self.is_tor_running():
                logger.info("Auto-starting Tor...")
                await self.start_tor()

        except Exception as e:
            logger.error(f"Failed to initialize Tor manager: {e}")
            raise

    async def is_tor_running(self) -> bool:
        """Check if Tor is running and accessible"""
        try:
            # Check if we can connect to the SOCKS proxy
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((self.tor_proxy_host, self.tor_proxy_port))
            sock.close()

            if result == 0:
                # Only attempt controller connection once
                if not self.controller_connection_attempted:
                    self.controller_connection_attempted = True
                    try:
                        with Controller.from_port(port=self.tor_control_port) as controller:
                            if self.tor_control_password:
                                controller.authenticate(password=self.tor_control_password)
                            else:
                                controller.authenticate()
                            self.controller_available = True
                            logger.info("Tor controller connection successful")
                    except Exception as e:
                        self.controller_available = False
                        logger.info(f"Tor controller not available: {e}")
                        logger.info("SOCKS proxy is running but control port isn't accessible")

                self.is_running = True
                return True

            self.is_running = False
            return False

        except Exception as e:
            logger.debug(f"Error checking Tor status: {e}")
            self.is_running = False
            return False

    async def start_tor(self) -> bool:
        """Start Tor process"""
        try:
            if await self.is_tor_running():
                logger.info("Tor is already running")
                return True

            # Check if tor is installed
            if not shutil.which('tor'):
                logger.error("Tor is not installed. Please install Tor first.")
                return False

            logger.info("Starting Tor...")

            # Create torrc configuration
            torrc_path = self.tor_data_dir / "torrc"
            await self._create_torrc(torrc_path)

            # Launch Tor with configuration
            tor_config = {
                'SocksPort': str(self.tor_proxy_port),
                'ControlPort': str(self.tor_control_port),
                'DataDirectory': str(self.tor_data_dir),
                'CookieAuthentication': '1',
            }

            # Add password authentication if specified
            if self.tor_control_password:
                tor_config['HashedControlPassword'] = self._hash_password(self.tor_control_password)
                tor_config['CookieAuthentication'] = '0'

            # Launch Tor process
            self.tor_process = launch_tor_with_config(
                config=tor_config,
                timeout=60,
                take_ownership=True
            )

            # Wait for Tor to be ready
            await self._wait_for_tor_ready()

            # Connect to controller
            await self._connect_controller()

            self.is_running = True
            logger.info(f"Tor started successfully on SOCKS port {self.tor_proxy_port}")
            return True

        except Exception as e:
            logger.error(f"Failed to start Tor: {e}")
            await self.stop_tor()
            return False

    async def stop_tor(self):
        """Stop Tor process"""
        try:
            logger.info("Stopping Tor...")

            # Close controller connection
            if self.controller:
                try:
                    self.controller.close()
                except Exception:
                    pass
                self.controller = None

            # Terminate Tor process
            if self.tor_process:
                try:
                    self.tor_process.kill()
                    self.tor_process.wait(timeout=10)
                except Exception:
                    pass
                self.tor_process = None

            self.is_running = False
            logger.info("Tor stopped")

        except Exception as e:
            logger.error(f"Error stopping Tor: {e}")

    async def new_circuit(self) -> bool:
        """Request a new Tor circuit"""
        try:
            # Only attempt if controller is available
            if not self.controller_available:
                logger.debug("Tor controller not available, cannot request new circuit")
                return False

            if not self.controller:
                await self._connect_controller()

            if self.controller:
                self.controller.signal(Signal.NEWNYM)
                logger.info("Requested new Tor circuit")
                # Wait a bit for the new circuit to be established
                await asyncio.sleep(2)
                return True

            return False

        except Exception as e:
            logger.error(f"Failed to create new circuit: {e}")
            return False

    async def get_status(self) -> Dict[str, Any]:
        """Get Tor status information"""
        status = {
            'running': await self.is_tor_running(),
            'proxy_host': self.tor_proxy_host,
            'proxy_port': self.tor_proxy_port,
            'control_port': self.tor_control_port,
            'controller_available': self.controller_available,
            'data_dir': str(self.tor_data_dir),
            'auto_start': self.auto_start,
        }

        if self.controller:
            try:
                status['version'] = str(self.controller.get_version())
                status['circuits'] = len(list(self.controller.get_circuits()))
            except Exception:
                pass

        return status

    async def _create_torrc(self, torrc_path: Path):
        """Create Tor configuration file"""
        torrc_content = f"""
# PassChanger Tor Configuration
SocksPort {self.tor_proxy_port}
ControlPort {self.tor_control_port}
DataDirectory {self.tor_data_dir}
CookieAuthentication 1

# Security settings
ExitPolicy reject *:*
DisableDebuggerAttachment 0

# Performance settings
CircuitBuildTimeout 30
LearnCircuitBuildTimeout 0
MaxCircuitDirtiness 600
NewCircuitPeriod 30
"""

        if self.tor_control_password:
            hashed_password = self._hash_password(self.tor_control_password)
            torrc_content += f"\nHashedControlPassword {hashed_password}\n"
            torrc_content += "CookieAuthentication 0\n"

        with open(torrc_path, 'w') as f:
            f.write(torrc_content)

    def _hash_password(self, password: str) -> str:
        """Hash password for Tor control authentication"""
        try:
            # Use stem to hash the password
            from stem.util.tor_tools import hash_password
            return hash_password(password)
        except ImportError:
            # Fallback: use tor --hash-password command
            try:
                result = subprocess.run(
                    ['tor', '--hash-password', password],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                if result.returncode == 0:
                    return result.stdout.strip().split('\n')[-1]
            except Exception:
                pass

            logger.warning("Could not hash password, using plaintext (insecure)")
            return password

    async def _wait_for_tor_ready(self, timeout: int = 60):
        """Wait for Tor to be ready"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if await self.is_tor_running():
                return True
            await asyncio.sleep(1)

        raise TimeoutError("Tor failed to start within timeout")

    async def _connect_controller(self):
        """Connect to Tor controller"""
        try:
            # Only attempt if we know the controller is available
            if not self.controller_available:
                logger.debug("Tor controller not available, skipping connection attempt")
                return

            self.controller = Controller.from_port(port=self.tor_control_port)

            if self.tor_control_password:
                self.controller.authenticate(password=self.tor_control_password)
            else:
                self.controller.authenticate()

            logger.debug("Connected to Tor controller")

        except Exception as e:
            logger.warning(f"Could not connect to Tor controller: {e}")
            self.controller = None

    async def close(self):
        """Close Tor manager and cleanup"""
        await self.stop_tor()
        logger.info("Tor manager closed")
